[debug] [2025-06-25T07:04:53.307Z] ----------------------------------------------------------------------
[debug] [2025-06-25T07:04:53.308Z] Command:       /usr/local/bin/node /usr/local/bin/firebase emulators:start --only functions
[debug] [2025-06-25T07:04:53.308Z] CLI Version:   14.8.0
[debug] [2025-06-25T07:04:53.308Z] Platform:      darwin
[debug] [2025-06-25T07:04:53.308Z] Node Version:  v22.13.0
[debug] [2025-06-25T07:04:53.309Z] Time:          Wed Jun 25 2025 15:04:53 GMT+0800 (Hong Kong Standard Time)
[debug] [2025-06-25T07:04:53.309Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-25T07:04:53.409Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-25T07:04:53.409Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-06-25T07:04:53.775Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:04:53.775Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-06-25T07:04:53.777Z] [hub] writing locator at /var/folders/xk/ttb_w1_n03sd65nxdccs61hr0000gq/T/hub-tkoh-central-auth.json
[debug] [2025-06-25T07:04:54.145Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:04:54.145Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:04:54.145Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:04:54.145Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-06-25T07:04:54.151Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] i  emulators: Shutting down emulators. {"metadata":{"emulator":{"name":"hub"},"message":"Shutting down emulators."}}
[info] i  functions: Stopping Functions Emulator {"metadata":{"emulator":{"name":"functions"},"message":"Stopping Functions Emulator"}}
[info] i  hub: Stopping emulator hub {"metadata":{"emulator":{"name":"hub"},"message":"Stopping emulator hub"}}
[debug] [2025-06-25T07:04:54.153Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[debug] [2025-06-25T07:04:54.203Z] Error: EACCES: permission denied, mkdir '/Users/<USER>/.config/firebase'
    at Object.mkdirSync (node:fs:1364:26)
    at credFilePath (/usr/local/lib/node_modules/firebase-tools/lib/defaultCredentials.js:73:12)
    at getCredentialPathAsync (/usr/local/lib/node_modules/firebase-tools/lib/defaultCredentials.js:10:22)
    at getCredentialsEnvironment (/usr/local/lib/node_modules/firebase-tools/lib/emulator/env.js:53:87)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async FunctionsEmulator.start (/usr/local/lib/node_modules/firebase-tools/lib/emulator/functionsEmulator.js:240:31)
    at async EmulatorRegistry.start (/usr/local/lib/node_modules/firebase-tools/lib/emulator/registry.js:19:9)
    at async Object.startAll (/usr/local/lib/node_modules/firebase-tools/lib/emulator/controller.js:394:9)
    at async /usr/local/lib/node_modules/firebase-tools/lib/commands/emulators-start.js:34:43
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-06-25T07:09:04.323Z] ----------------------------------------------------------------------
[debug] [2025-06-25T07:09:04.324Z] Command:       /usr/local/bin/node /usr/local/bin/firebase emulators:start --only functions
[debug] [2025-06-25T07:09:04.325Z] CLI Version:   14.8.0
[debug] [2025-06-25T07:09:04.325Z] Platform:      darwin
[debug] [2025-06-25T07:09:04.325Z] Node Version:  v22.13.0
[debug] [2025-06-25T07:09:04.325Z] Time:          Wed Jun 25 2025 15:09:04 GMT+0800 (Hong Kong Standard Time)
[debug] [2025-06-25T07:09:04.325Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-25T07:09:04.424Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-25T07:09:04.425Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-06-25T07:09:04.812Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:09:04.812Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-06-25T07:09:04.814Z] [hub] writing locator at /var/folders/xk/ttb_w1_n03sd65nxdccs61hr0000gq/T/hub-tkoh-central-auth.json
[debug] [2025-06-25T07:09:05.200Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:09:05.200Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:09:05.200Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:09:05.200Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5002}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-06-25T07:09:05.207Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] i  emulators: Shutting down emulators. {"metadata":{"emulator":{"name":"hub"},"message":"Shutting down emulators."}}
[info] i  functions: Stopping Functions Emulator {"metadata":{"emulator":{"name":"functions"},"message":"Stopping Functions Emulator"}}
[info] i  hub: Stopping emulator hub {"metadata":{"emulator":{"name":"hub"},"message":"Stopping emulator hub"}}
[debug] [2025-06-25T07:09:05.208Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[debug] [2025-06-25T07:09:05.260Z] Error: EACCES: permission denied, mkdir '/Users/<USER>/.config/firebase'
    at Object.mkdirSync (node:fs:1364:26)
    at credFilePath (/usr/local/lib/node_modules/firebase-tools/lib/defaultCredentials.js:73:12)
    at getCredentialPathAsync (/usr/local/lib/node_modules/firebase-tools/lib/defaultCredentials.js:10:22)
    at getCredentialsEnvironment (/usr/local/lib/node_modules/firebase-tools/lib/emulator/env.js:53:87)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async FunctionsEmulator.start (/usr/local/lib/node_modules/firebase-tools/lib/emulator/functionsEmulator.js:240:31)
    at async EmulatorRegistry.start (/usr/local/lib/node_modules/firebase-tools/lib/emulator/registry.js:19:9)
    at async Object.startAll (/usr/local/lib/node_modules/firebase-tools/lib/emulator/controller.js:394:9)
    at async /usr/local/lib/node_modules/firebase-tools/lib/commands/emulators-start.js:34:43
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-06-25T07:09:42.896Z] ----------------------------------------------------------------------
[debug] [2025-06-25T07:09:42.897Z] Command:       /usr/local/bin/node /Users/<USER>/Documents/GitHub/central-auth/node_modules/.bin/firebase emulators:start --only functions
[debug] [2025-06-25T07:09:42.897Z] CLI Version:   14.8.0
[debug] [2025-06-25T07:09:42.897Z] Platform:      darwin
[debug] [2025-06-25T07:09:42.897Z] Node Version:  v22.13.0
[debug] [2025-06-25T07:09:42.897Z] Time:          Wed Jun 25 2025 15:09:42 GMT+0800 (Hong Kong Standard Time)
[debug] [2025-06-25T07:09:42.897Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-25T07:09:43.003Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-25T07:09:43.003Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-06-25T07:09:43.383Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:09:43.383Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-06-25T07:09:43.385Z] [hub] writing locator at /var/folders/xk/ttb_w1_n03sd65nxdccs61hr0000gq/T/hub-tkoh-central-auth.json
[debug] [2025-06-25T07:09:43.761Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:09:43.761Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:09:43.761Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T07:09:43.761Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5002}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-06-25T07:09:43.768Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] i  emulators: Shutting down emulators. {"metadata":{"emulator":{"name":"hub"},"message":"Shutting down emulators."}}
[info] i  functions: Stopping Functions Emulator {"metadata":{"emulator":{"name":"functions"},"message":"Stopping Functions Emulator"}}
[info] i  hub: Stopping emulator hub {"metadata":{"emulator":{"name":"hub"},"message":"Stopping emulator hub"}}
[debug] [2025-06-25T07:09:43.769Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[debug] [2025-06-25T07:09:43.818Z] Error: EACCES: permission denied, mkdir '/Users/<USER>/.config/firebase'
    at Object.mkdirSync (node:fs:1364:26)
    at credFilePath (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/defaultCredentials.js:73:12)
    at getCredentialPathAsync (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/defaultCredentials.js:10:22)
    at getCredentialsEnvironment (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/env.js:53:87)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async FunctionsEmulator.start (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/functionsEmulator.js:240:31)
    at async EmulatorRegistry.start (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/registry.js:19:9)
    at async Object.startAll (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/controller.js:394:9)
    at async /Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/commands/emulators-start.js:34:43
[error] 
[error] Error: An unexpected error has occurred.

rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - only allow access through Cloud Functions
    match /users/{employeeNumber} {
      // Deny all direct client access to user documents
      // All operations should go through the Cloud Functions API
      allow read, write: if false;
    }

    // Departments collection - allow read access through Cloud Functions
    match /departments/{document} {
      // Allow read access through Cloud Functions only
      allow read, write: if false;
    }

    // Units collection - allow read access through Cloud Functions
    match /units/{document} {
      // Allow read access through Cloud Functions only
      allow read, write: if false;
    }

    // Deny access to all other collections by default
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
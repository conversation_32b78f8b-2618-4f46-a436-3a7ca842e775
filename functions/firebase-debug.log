[debug] [2025-06-24T09:49:58.915Z] ----------------------------------------------------------------------
[debug] [2025-06-24T09:49:58.917Z] Command:       /usr/local/bin/node /Users/<USER>/Documents/GitHub/central-auth/node_modules/.bin/firebase emulators:start --only functions
[debug] [2025-06-24T09:49:58.917Z] CLI Version:   14.8.0
[debug] [2025-06-24T09:49:58.917Z] Platform:      darwin
[debug] [2025-06-24T09:49:58.917Z] Node Version:  v22.13.0
[debug] [2025-06-24T09:49:58.918Z] Time:          Tue Jun 24 2025 17:49:58 GMT+0800 (Hong Kong Standard Time)
[debug] [2025-06-24T09:49:58.918Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-24T09:49:59.032Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-24T09:49:59.033Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-06-24T09:49:59.427Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-24T09:49:59.427Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-06-24T09:49:59.429Z] [hub] writing locator at /var/folders/xk/ttb_w1_n03sd65nxdccs61hr0000gq/T/hub-tkoh-central-auth.json
[debug] [2025-06-24T09:49:59.822Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-24T09:49:59.822Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-24T09:49:59.822Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-24T09:49:59.822Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-06-24T09:49:59.829Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] i  emulators: Shutting down emulators. {"metadata":{"emulator":{"name":"hub"},"message":"Shutting down emulators."}}
[info] i  functions: Stopping Functions Emulator {"metadata":{"emulator":{"name":"functions"},"message":"Stopping Functions Emulator"}}
[info] i  hub: Stopping emulator hub {"metadata":{"emulator":{"name":"hub"},"message":"Stopping emulator hub"}}
[debug] [2025-06-24T09:49:59.831Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[debug] [2025-06-24T09:49:59.886Z] Error: EACCES: permission denied, mkdir '/Users/<USER>/.config/firebase'
    at Object.mkdirSync (node:fs:1364:26)
    at credFilePath (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/defaultCredentials.js:73:12)
    at getCredentialPathAsync (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/defaultCredentials.js:10:22)
    at getCredentialsEnvironment (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/env.js:53:87)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async FunctionsEmulator.start (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/functionsEmulator.js:240:31)
    at async EmulatorRegistry.start (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/registry.js:19:9)
    at async Object.startAll (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/controller.js:394:9)
    at async /Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/commands/emulators-start.js:34:43
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-06-25T06:02:26.571Z] ----------------------------------------------------------------------
[debug] [2025-06-25T06:02:26.573Z] Command:       /usr/local/bin/node /Users/<USER>/Documents/GitHub/central-auth/node_modules/.bin/firebase emulators:start --only functions
[debug] [2025-06-25T06:02:26.573Z] CLI Version:   14.8.0
[debug] [2025-06-25T06:02:26.573Z] Platform:      darwin
[debug] [2025-06-25T06:02:26.573Z] Node Version:  v22.13.0
[debug] [2025-06-25T06:02:26.573Z] Time:          Wed Jun 25 2025 14:02:26 GMT+0800 (Hong Kong Standard Time)
[debug] [2025-06-25T06:02:26.573Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-25T06:02:26.690Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-25T06:02:26.691Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-06-25T06:02:27.080Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T06:02:27.080Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-06-25T06:02:27.083Z] [hub] writing locator at /var/folders/xk/ttb_w1_n03sd65nxdccs61hr0000gq/T/hub-tkoh-central-auth.json
[debug] [2025-06-25T06:02:27.465Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T06:02:27.465Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T06:02:27.465Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-25T06:02:27.465Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-06-25T06:02:27.503Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] i  emulators: Shutting down emulators. {"metadata":{"emulator":{"name":"hub"},"message":"Shutting down emulators."}}
[info] i  functions: Stopping Functions Emulator {"metadata":{"emulator":{"name":"functions"},"message":"Stopping Functions Emulator"}}
[info] i  hub: Stopping emulator hub {"metadata":{"emulator":{"name":"hub"},"message":"Stopping emulator hub"}}
[debug] [2025-06-25T06:02:27.504Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[debug] [2025-06-25T06:02:27.554Z] Error: EACCES: permission denied, mkdir '/Users/<USER>/.config/firebase'
    at Object.mkdirSync (node:fs:1364:26)
    at credFilePath (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/defaultCredentials.js:73:12)
    at getCredentialPathAsync (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/defaultCredentials.js:10:22)
    at getCredentialsEnvironment (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/env.js:53:87)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async FunctionsEmulator.start (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/functionsEmulator.js:240:31)
    at async EmulatorRegistry.start (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/registry.js:19:9)
    at async Object.startAll (/Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/emulator/controller.js:394:9)
    at async /Users/<USER>/Documents/GitHub/central-auth/node_modules/firebase-tools/lib/commands/emulators-start.js:34:43
[error] 
[error] Error: An unexpected error has occurred.

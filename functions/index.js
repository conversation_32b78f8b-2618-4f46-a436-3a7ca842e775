const { onRequest } = require('firebase-functions/v2/https');
const { setGlobalOptions } = require('firebase-functions/v2');
const admin = require('firebase-admin');
const cors = require('cors')({ origin: true });
const bcrypt = require('bcryptjs');

// Set global options
setGlobalOptions({ maxInstances: 10 });

// Initialize Firebase Admin
admin.initializeApp();
const db = admin.firestore();

// Helper function to handle CORS
const handleCors = (req, res, handler) => {
  return cors(req, res, () => handler(req, res));
};

// Helper function for error responses
const sendError = (res, status, message, details = null) => {
  const response = { success: false, message };
  if (details) response.details = details;
  res.status(status).json(response);
};

// Helper function for success responses
const sendSuccess = (res, message, data = null, status = 200) => {
  const response = { success: true, message };
  if (data) response.data = data;
  res.status(status).json(response);
};

// Validation helper
const validateUserData = (userData, isUpdate = false) => {
  const errors = [];

  if (!isUpdate && !userData.employeeNumber) {
    errors.push('Employee number is required');
  }
  if (!isUpdate && !userData.corpId) {
    errors.push('Corporate ID is required');
  }
  if (!isUpdate && !userData.password) {
    errors.push('Password is required');
  }
  if (userData.password && userData.password.length < 6) {
    errors.push('Password must be at least 6 characters');
  }
  if (!isUpdate && !userData.email) {
    errors.push('Email is required');
  }
  if (userData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
    errors.push('Invalid email format');
  }
  if (!isUpdate && !userData.department) {
    errors.push('Department is required');
  }
  if (!isUpdate && !userData.unit) {
    errors.push('Unit is required');
  }

  return errors;
};

// Main API function
exports.api = onRequest(async (req, res) => {
  return handleCors(req, res, async () => {
    try {
      const path = req.path;
      const method = req.method;

      // Route: POST /api/auth/login
      if (path === '/auth/login' && method === 'POST') {
        const { employeeNumber, password } = req.body;

        if (!employeeNumber || !password) {
          return sendError(res, 400, 'Employee number and password are required');
        }

        try {
          const userDoc = await db.collection('users').doc(employeeNumber).get();

          if (!userDoc.exists) {
            return sendError(res, 401, 'Invalid employee number or password');
          }

          const userData = userDoc.data();

          if (userData.suspended) {
            return sendError(res, 401, 'User account is suspended');
          }

          const isPasswordValid = await bcrypt.compare(password, userData.password);
          if (!isPasswordValid) {
            return sendError(res, 401, 'Invalid employee number or password');
          }

          // Remove password from response
          const { password: _, ...userWithoutPassword } = userData;
          return sendSuccess(res, 'Authentication successful', { user: userWithoutPassword });

        } catch (error) {
          console.error('Authentication error:', error);
          return sendError(res, 500, 'Authentication failed');
        }
      }

      // Route: POST /api/auth/verify (for external webapp integration)
      if (path === '/auth/verify' && method === 'POST') {
        const { employeeNumber, password } = req.body;

        if (!employeeNumber || !password) {
          return res.json({
            success: false,
            authenticated: false,
            message: 'Employee number and password are required'
          });
        }

        try {
          const userDoc = await db.collection('users').doc(employeeNumber).get();

          if (!userDoc.exists) {
            return res.json({
              success: false,
              authenticated: false,
              message: 'Invalid credentials'
            });
          }

          const userData = userDoc.data();

          if (userData.suspended) {
            return res.json({
              success: false,
              authenticated: false,
              message: 'User account is suspended'
            });
          }

          const isPasswordValid = await bcrypt.compare(password, userData.password);
          if (!isPasswordValid) {
            return res.json({
              success: false,
              authenticated: false,
              message: 'Invalid credentials'
            });
          }

          return res.json({
            success: true,
            authenticated: true,
            message: 'User verified',
            data: {
              employeeNumber: userData.employeeNumber,
              corpId: userData.corpId,
              email: userData.email,
              department: userData.department,
              unit: userData.unit
            }
          });

        } catch (error) {
          console.error('Verification error:', error);
          return res.json({
            success: false,
            authenticated: false,
            message: 'Verification failed'
          });
        }
      }

      // Route: GET /api/users - Get all users
      if (path === '/users' && method === 'GET') {
        try {
          const { limit = 20, startAfter } = req.query;

          let query = db.collection('users').orderBy('employeeNumber');

          if (limit) {
            query = query.limit(parseInt(limit));
          }

          if (startAfter) {
            const startAfterDoc = await db.collection('users').doc(startAfter).get();
            if (startAfterDoc.exists) {
              query = query.startAfter(startAfterDoc);
            }
          }

          const snapshot = await query.get();
          const users = [];

          snapshot.forEach(doc => {
            const userData = doc.data();
            const { password, ...userWithoutPassword } = userData;
            users.push(userWithoutPassword);
          });

          return sendSuccess(res, 'Users retrieved successfully', { users, count: users.length });

        } catch (error) {
          console.error('Get users error:', error);
          return sendError(res, 500, 'Failed to retrieve users');
        }
      }

      // Route: GET /api/users/search - Search users
      if (path === '/users/search' && method === 'GET') {
        try {
          const { searchTerm, searchField = 'department' } = req.query;

          if (!searchTerm) {
            return sendError(res, 400, 'Search term is required');
          }

          if (!['department', 'unit'].includes(searchField)) {
            return sendError(res, 400, 'Search field must be department or unit');
          }

          const snapshot = await db.collection('users')
            .where(searchField, '==', searchTerm)
            .get();

          const users = [];
          snapshot.forEach(doc => {
            const userData = doc.data();
            const { password, ...userWithoutPassword } = userData;
            users.push(userWithoutPassword);
          });

          return sendSuccess(res, 'Search completed successfully', {
            users,
            count: users.length,
            searchTerm,
            searchField
          });

        } catch (error) {
          console.error('Search users error:', error);
          return sendError(res, 500, 'Search failed');
        }
      }

      // Route: GET /api/users/:employeeNumber - Get user by employee number
      if (path.startsWith('/users/') && method === 'GET' && !path.includes('/search')) {
        try {
          const employeeNumber = path.split('/users/')[1];

          if (!employeeNumber) {
            return sendError(res, 400, 'Employee number is required');
          }

          const userDoc = await db.collection('users').doc(employeeNumber).get();

          if (!userDoc.exists) {
            return sendError(res, 404, 'User not found');
          }

          const userData = userDoc.data();
          const { password, ...userWithoutPassword } = userData;

          return sendSuccess(res, 'User retrieved successfully', { user: userWithoutPassword });

        } catch (error) {
          console.error('Get user error:', error);
          return sendError(res, 500, 'Failed to retrieve user');
        }
      }

      // Route: POST /api/users - Create new user
      if (path === '/users' && method === 'POST') {
        try {
          const userData = req.body;

          // Validate input
          const validationErrors = validateUserData(userData);
          if (validationErrors.length > 0) {
            return sendError(res, 400, 'Validation error', validationErrors);
          }

          // Check if user already exists
          const existingUser = await db.collection('users').doc(userData.employeeNumber).get();
          if (existingUser.exists) {
            return sendError(res, 409, 'User with this employee number already exists');
          }

          // Hash password
          const hashedPassword = await bcrypt.hash(userData.password, 12);

          // Create user document
          const userDoc = {
            employeeNumber: userData.employeeNumber,
            corpId: userData.corpId,
            password: hashedPassword,
            email: userData.email,
            department: userData.department,
            unit: userData.unit,
            suspended: userData.suspended || false,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          };

          // Save to Firestore
          await db.collection('users').doc(userData.employeeNumber).set(userDoc);

          // Return user data without password
          const { password: _, ...userWithoutPassword } = userDoc;
          return sendSuccess(res, 'User created successfully', { user: userWithoutPassword }, 201);

        } catch (error) {
          console.error('Create user error:', error);
          return sendError(res, 500, 'Failed to create user');
        }
      }

      // Route: PUT /api/users/:employeeNumber - Update user
      if (path.startsWith('/users/') && method === 'PUT') {
        try {
          const employeeNumber = path.split('/users/')[1];
          const updateData = req.body;

          if (!employeeNumber) {
            return sendError(res, 400, 'Employee number is required');
          }

          // Validate input
          const validationErrors = validateUserData(updateData, true);
          if (validationErrors.length > 0) {
            return sendError(res, 400, 'Validation error', validationErrors);
          }

          // Check if user exists
          const userDoc = await db.collection('users').doc(employeeNumber).get();
          if (!userDoc.exists) {
            return sendError(res, 404, 'User not found');
          }

          // Prepare update data
          const updates = { ...updateData };

          // Hash password if it's being updated
          if (updates.password) {
            updates.password = await bcrypt.hash(updates.password, 12);
          }

          // Add updated timestamp
          updates.updatedAt = admin.firestore.FieldValue.serverTimestamp();

          // Update document
          await db.collection('users').doc(employeeNumber).update(updates);

          // Get updated user data
          const updatedUserDoc = await db.collection('users').doc(employeeNumber).get();
          const userData = updatedUserDoc.data();
          const { password, ...userWithoutPassword } = userData;

          return sendSuccess(res, 'User updated successfully', { user: userWithoutPassword });

        } catch (error) {
          console.error('Update user error:', error);
          return sendError(res, 500, 'Failed to update user');
        }
      }

      // Route: DELETE /api/users/:employeeNumber - Delete user
      if (path.startsWith('/users/') && method === 'DELETE') {
        try {
          const employeeNumber = path.split('/users/')[1];

          if (!employeeNumber) {
            return sendError(res, 400, 'Employee number is required');
          }

          // Check if user exists
          const userDoc = await db.collection('users').doc(employeeNumber).get();
          if (!userDoc.exists) {
            return sendError(res, 404, 'User not found');
          }

          // Delete user
          await db.collection('users').doc(employeeNumber).delete();

          return sendSuccess(res, 'User deleted successfully');

        } catch (error) {
          console.error('Delete user error:', error);
          return sendError(res, 500, 'Failed to delete user');
        }
      }

      // Route: GET /api/departments - Get all departments
      if (path === '/departments' && method === 'GET') {
        try {
          const departmentsDoc = await db.collection('departments').doc('list').get();

          if (!departmentsDoc.exists) {
            return sendError(res, 404, 'Departments not found');
          }

          const departmentsData = departmentsDoc.data();
          return sendSuccess(res, 'Departments retrieved successfully', {
            departments: departmentsData.departments
          });

        } catch (error) {
          console.error('Get departments error:', error);
          return sendError(res, 500, 'Failed to retrieve departments');
        }
      }

      // Route: GET /api/units - Get all units (optionally filtered by department)
      if (path === '/units' && method === 'GET') {
        try {
          const { department } = req.query;

          let query = db.collection('units');

          if (department) {
            query = query.where('department', '==', department);
          }

          query = query.orderBy('name');

          const snapshot = await query.get();
          const units = [];

          snapshot.forEach(doc => {
            units.push({
              id: doc.id,
              ...doc.data()
            });
          });

          return sendSuccess(res, 'Units retrieved successfully', { units });

        } catch (error) {
          console.error('Get units error:', error);
          return sendError(res, 500, 'Failed to retrieve units');
        }
      }

      // Route not found
      return sendError(res, 404, `Route ${method} ${path} not found`);

    } catch (error) {
      console.error('API Error:', error);
      return sendError(res, 500, 'Internal server error');
    }
  });
});
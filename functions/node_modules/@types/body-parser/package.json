{"name": "@types/body-parser", "version": "1.19.6", "description": "TypeScript definitions for body-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic", "url": "https://github.com/vilic"}, {"name": "<PERSON>", "githubUsername": "dreampulse", "url": "https://github.com/dreampulse"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk", "url": "https://github.com/blendsdk"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tlaziuk"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/jwalton"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/body-parser"}, "scripts": {}, "dependencies": {"@types/connect": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "d788c843f427d6ca19640ee90eb433324a18f23aed05402a82c4e47e6d60b29d", "typeScriptVersion": "5.1"}
const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');

const app = express();
const port = 5001;

// Middleware
app.use(cors());
app.use(express.json());

// Mock database for development
const mockDepartments = [
  'IT', 'HR', 'Finance', 'Marketing', 'Operations', 
  'Sales', 'Legal', 'Administration', 'Engineering', 'Customer Service'
];

const mockUnits = [
  // IT Department Units
  { name: 'Software Development', department: 'IT' },
  { name: 'Infrastructure', department: 'IT' },
  { name: 'Cybersecurity', department: 'IT' },
  { name: 'Data Analytics', department: 'IT' },
  { name: 'Technical Support', department: 'IT' },
  
  // HR Department Units
  { name: 'Recruitment', department: 'HR' },
  { name: 'Employee Relations', department: 'HR' },
  { name: 'Training & Development', department: 'HR' },
  { name: 'Compensation & Benefits', department: 'HR' },
  
  // Finance Department Units
  { name: 'Accounting', department: 'Finance' },
  { name: 'Financial Planning', department: 'Finance' },
  { name: 'Audit', department: 'Finance' },
  { name: 'Treasury', department: 'Finance' },
  
  // Marketing Department Units
  { name: 'Digital Marketing', department: 'Marketing' },
  { name: 'Brand Management', department: 'Marketing' },
  { name: 'Market Research', department: 'Marketing' },
  { name: 'Content Creation', department: 'Marketing' },
  
  // Operations Department Units
  { name: 'Supply Chain', department: 'Operations' },
  { name: 'Quality Assurance', department: 'Operations' },
  { name: 'Process Improvement', department: 'Operations' },
  { name: 'Facilities Management', department: 'Operations' },
  
  // Sales Department Units
  { name: 'Inside Sales', department: 'Sales' },
  { name: 'Field Sales', department: 'Sales' },
  { name: 'Sales Operations', department: 'Sales' },
  { name: 'Business Development', department: 'Sales' },
  
  // Legal Department Units
  { name: 'Corporate Law', department: 'Legal' },
  { name: 'Compliance', department: 'Legal' },
  { name: 'Contracts', department: 'Legal' },
  
  // Administration Department Units
  { name: 'Executive Support', department: 'Administration' },
  { name: 'Office Management', department: 'Administration' },
  { name: 'Records Management', department: 'Administration' },
  
  // Engineering Department Units
  { name: 'Product Engineering', department: 'Engineering' },
  { name: 'Research & Development', department: 'Engineering' },
  { name: 'Quality Engineering', department: 'Engineering' },
  
  // Customer Service Department Units
  { name: 'Customer Support', department: 'Customer Service' },
  { name: 'Technical Support', department: 'Customer Service' },
  { name: 'Customer Success', department: 'Customer Service' }
];

const mockUsers = [
  {
    employeeNumber: '12345',
    corpId: 'test.user',
    email: '<EMAIL>',
    password: bcrypt.hashSync('password123', 10),
    department: 'IT',
    unit: 'Software Development',
    suspended: false
  }
];

// Helper functions
const sendSuccess = (res, message, data = null) => {
  res.json({
    success: true,
    message,
    data
  });
};

const sendError = (res, status, message) => {
  res.status(status).json({
    success: false,
    message
  });
};

// Routes
app.get('/tkoh-central-auth/us-central1/api/departments', (req, res) => {
  sendSuccess(res, 'Departments retrieved successfully', { departments: mockDepartments });
});

app.get('/tkoh-central-auth/us-central1/api/units', (req, res) => {
  const { department } = req.query;
  let units = mockUnits;
  
  if (department) {
    units = mockUnits.filter(unit => unit.department === department);
  }
  
  sendSuccess(res, 'Units retrieved successfully', { units });
});

app.post('/tkoh-central-auth/us-central1/api/auth/verify', (req, res) => {
  const { employeeNumber, password } = req.body;
  
  const user = mockUsers.find(u => u.employeeNumber === employeeNumber);
  
  if (!user) {
    return sendError(res, 401, 'Invalid credentials');
  }
  
  if (!bcrypt.compareSync(password, user.password)) {
    return sendError(res, 401, 'Invalid credentials');
  }
  
  if (user.suspended) {
    return sendError(res, 401, 'Account suspended');
  }
  
  const { password: _, ...userWithoutPassword } = user;
  sendSuccess(res, 'Authentication successful', { 
    authenticated: true, 
    user: userWithoutPassword 
  });
});

app.get('/tkoh-central-auth/us-central1/api/users', (req, res) => {
  const usersWithoutPasswords = mockUsers.map(({ password, ...user }) => user);
  sendSuccess(res, 'Users retrieved successfully', { 
    users: usersWithoutPasswords,
    total: usersWithoutPasswords.length 
  });
});

// Catch all for API routes
app.all('/tkoh-central-auth/us-central1/api/*', (req, res) => {
  sendError(res, 404, `Route ${req.method} ${req.path} not found`);
});

// Start server
app.listen(port, () => {
  console.log(`🚀 Development server running at http://localhost:${port}`);
  console.log(`📡 API available at http://localhost:${port}/tkoh-central-auth/us-central1/api`);
  console.log('');
  console.log('Available endpoints:');
  console.log('  GET  /tkoh-central-auth/us-central1/api/departments');
  console.log('  GET  /tkoh-central-auth/us-central1/api/units');
  console.log('  POST /tkoh-central-auth/us-central1/api/auth/verify');
  console.log('  GET  /tkoh-central-auth/us-central1/api/users');
});

module.exports = app;

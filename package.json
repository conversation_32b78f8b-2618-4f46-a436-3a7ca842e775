{"name": "central-auth", "version": "1.0.0", "description": "Central authentication system with Firebase Firestore, Cloud Functions, and Vue.js admin interface", "main": "index.js", "scripts": {"dev:functions": "firebase emulators:start --only functions", "dev:frontend": "cd frontend && npm run dev", "dev": "concurrently \"npm run dev:functions\" \"npm run dev:frontend\"", "build:functions": "cd functions && npm run deploy", "build:frontend": "cd frontend && npm run build", "build": "npm run build:frontend && npm run build:functions", "deploy": "firebase deploy", "install:all": "npm install && cd functions && npm install && cd ../frontend && npm install", "emulators": "firebase emulators:start", "serve": "firebase serve"}, "keywords": ["authentication", "firebase", "vue", "cloud-functions"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "firebase-tools": "^14.8.0"}}
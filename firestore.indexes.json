{"indexes": [{"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "department", "order": "ASCENDING"}, {"fieldPath": "employeeNumber", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "unit", "order": "ASCENDING"}, {"fieldPath": "employeeNumber", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "suspended", "order": "ASCENDING"}, {"fieldPath": "employeeNumber", "order": "ASCENDING"}]}, {"collectionGroup": "units", "queryScope": "COLLECTION", "fields": [{"fieldPath": "department", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}], "fieldOverrides": []}
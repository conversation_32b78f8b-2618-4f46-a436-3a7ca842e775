# Central Authentication System

A centralized authentication system built with Firebase Firestore, Cloud Functions, and Vue.js admin interface with dynamic department and unit management.

## Features

- **Central User Database**: Firebase Firestore database storing user accounts with employee number, corp id, password, email, department, unit, and suspended status
- **Dynamic Department & Unit Management**: Database-driven departments and units with cascading dropdowns
- **REST API**: Firebase Cloud Functions providing secure API endpoints for authentication and user management
- **Admin Interface**: Vue.js application with PrimeVue 4.x components for admin management
- **Firebase Authentication**: Admin access control using Firebase Authentication
- **Firebase Hosting**: Deploy the entire application to Firebase for seamless integration

## Project Structure

```
central-auth/
├── functions/             # Firebase Cloud Functions (API backend)
│   ├── index.js          # Main Cloud Function with all API routes
│   └── package.json      # Functions dependencies
├── frontend/              # Vue.js admin interface
│   ├── src/
│   │   ├── components/    # Vue components
│   │   ├── views/         # Page views (Login, Dashboard, Users, etc.)
│   │   ├── stores/        # Pinia stores (auth, users, departments)
│   │   ├── config/        # Firebase configuration
│   │   └── router/        # Vue Router configuration
│   └── package.json
├── firebase.json          # Firebase project configuration
├── firestore.rules        # Firestore security rules
├── firestore.indexes.json # Firestore database indexes
├── setup-database.js     # Database initialization script
└── package.json           # Root package.json with scripts
```

## Setup Instructions

### Prerequisites
- Node.js (v18 or higher)
- Firebase CLI
- Firebase project with Firestore and Authentication enabled

### Installation

1. Clone the repository and install dependencies:
```bash
npm run install:all
```

2. Configure Firebase:
   - Create a Firebase project at https://console.firebase.google.com
   - Enable Firestore Database and Authentication
   - Copy `frontend/.env.example` to `frontend/.env`
   - Fill in your Firebase project configuration

3. Initialize Firebase (if not already done):
```bash
firebase login
firebase init
```

4. Initialize the database with departments and units:
```bash
# First, update the project ID in setup-database.js
node setup-database.js
```

5. Start development servers:
```bash
npm run dev
```

This will start both the Firebase Functions emulator and Vue frontend.
- Frontend: http://localhost:5173
- Functions: http://localhost:5001

6. Deploy to Firebase:
```bash
npm run deploy
```

### Environment Variables

Create a `.env` file in the `frontend/` directory:

```env
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
VITE_FIREBASE_APP_ID=your-app-id
VITE_API_BASE_URL=http://localhost:5001/your-project-id/us-central1/api
```

## API Endpoints (Firebase Functions)

### Authentication
- `POST /api/auth/login` - Authenticate user with employee number and password
- `POST /api/auth/verify` - Verify user credentials (for external webapp integration)

### User Management
- `GET /api/users` - Get all users (with pagination)
- `GET /api/users/search` - Search users by department or unit
- `GET /api/users/:employeeNumber` - Get user account details
- `POST /api/users` - Create new user account
- `PUT /api/users/:employeeNumber` - Update user account
- `DELETE /api/users/:employeeNumber` - Delete user account

### Department & Unit Management
- `GET /api/departments` - Get all departments
- `GET /api/units` - Get all units (optionally filtered by department)
- `GET /api/units?department=IT` - Get units for specific department

## Database Schema

### Users Collection (`users`)
User accounts are stored in Firestore with the following structure:

```javascript
{
  employeeNumber: "string",    // Document ID
  corpId: "string",
  password: "string",          // Hashed with bcrypt
  email: "string",
  department: "string",
  unit: "string",
  suspended: false,            // Boolean, default false
  createdAt: "timestamp",
  updatedAt: "timestamp"
}
```

### Departments Collection (`departments`)
```javascript
{
  departments: [               // Array of department names
    "IT",
    "HR",
    "Finance",
    "Marketing",
    // ... more departments
  ],
  createdAt: "timestamp",
  updatedAt: "timestamp"
}
```

### Units Collection (`units`)
```javascript
{
  name: "string",              // Unit name
  department: "string",        // Department this unit belongs to
  createdAt: "timestamp",
  updatedAt: "timestamp"
}
```

## Using the API in External Projects

This authentication system provides REST API endpoints that can be integrated into any web application or service. Here are examples in different programming languages:

### JavaScript/Node.js

#### Authentication
```javascript
// Using fetch API
async function authenticateUser(employeeNumber, password) {
  try {
    const response = await fetch('https://your-project-id.cloudfunctions.net/api/auth/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        employeeNumber: employeeNumber,
        password: password
      })
    });

    const result = await response.json();

    if (result.success) {
      console.log('Authentication successful:', result.data.user);
      return result.data.user;
    } else {
      console.error('Authentication failed:', result.message);
      return null;
    }
  } catch (error) {
    console.error('Error:', error);
    return null;
  }
}

// Usage
const user = await authenticateUser('12345', 'userpassword');
```

#### Get Departments and Units
```javascript
// Get all departments
async function getDepartments() {
  const response = await fetch('https://your-project-id.cloudfunctions.net/api/departments');
  const result = await response.json();
  return result.success ? result.data.departments : [];
}

// Get units for a specific department
async function getUnits(department = null) {
  const url = department
    ? `https://your-project-id.cloudfunctions.net/api/units?department=${encodeURIComponent(department)}`
    : 'https://your-project-id.cloudfunctions.net/api/units';

  const response = await fetch(url);
  const result = await response.json();
  return result.success ? result.data.units : [];
}

// Usage
const departments = await getDepartments();
const itUnits = await getUnits('IT');
```

### Python

#### Authentication
```python
import requests
import json

def authenticate_user(employee_number, password):
    """Authenticate user with the central auth system"""
    url = 'https://your-project-id.cloudfunctions.net/api/auth/verify'

    payload = {
        'employeeNumber': employee_number,
        'password': password
    }

    try:
        response = requests.post(url, json=payload)
        result = response.json()

        if result.get('success'):
            print(f"Authentication successful: {result['data']['user']}")
            return result['data']['user']
        else:
            print(f"Authentication failed: {result.get('message')}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return None

# Usage
user = authenticate_user('12345', 'userpassword')
if user:
    print(f"Welcome {user['corpId']}!")
```

#### Get Departments and Units
```python
def get_departments():
    """Get all departments from the central auth system"""
    url = 'https://your-project-id.cloudfunctions.net/api/departments'

    try:
        response = requests.get(url)
        result = response.json()
        return result['data']['departments'] if result.get('success') else []
    except requests.exceptions.RequestException as e:
        print(f"Error fetching departments: {e}")
        return []

def get_units(department=None):
    """Get units, optionally filtered by department"""
    url = 'https://your-project-id.cloudfunctions.net/api/units'
    params = {'department': department} if department else {}

    try:
        response = requests.get(url, params=params)
        result = response.json()
        return result['data']['units'] if result.get('success') else []
    except requests.exceptions.RequestException as e:
        print(f"Error fetching units: {e}")
        return []

# Usage
departments = get_departments()
it_units = get_units('IT')
```

### PHP

#### Authentication
```php
<?php
function authenticateUser($employeeNumber, $password) {
    $url = 'https://your-project-id.cloudfunctions.net/api/auth/verify';

    $data = array(
        'employeeNumber' => $employeeNumber,
        'password' => $password
    );

    $options = array(
        'http' => array(
            'header'  => "Content-type: application/json\r\n",
            'method'  => 'POST',
            'content' => json_encode($data)
        )
    );

    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);

    if ($result === FALSE) {
        return null;
    }

    $response = json_decode($result, true);

    if ($response['success']) {
        return $response['data']['user'];
    }

    return null;
}

// Usage
$user = authenticateUser('12345', 'userpassword');
if ($user) {
    echo "Welcome " . $user['corpId'] . "!";
}
?>
```

### cURL Examples

#### Authentication
```bash
curl -X POST https://your-project-id.cloudfunctions.net/api/auth/verify \
  -H "Content-Type: application/json" \
  -d '{
    "employeeNumber": "12345",
    "password": "userpassword"
  }'
```

#### Get Departments
```bash
curl https://your-project-id.cloudfunctions.net/api/departments
```

#### Get Units for IT Department
```bash
curl "https://your-project-id.cloudfunctions.net/api/units?department=IT"
```

### Integration Tips

1. **Error Handling**: Always check the `success` field in the response
2. **HTTPS**: Use HTTPS URLs for production deployments
3. **Rate Limiting**: Implement appropriate rate limiting in your applications
4. **Caching**: Consider caching department and unit data as they change infrequently
5. **Security**: Never expose user passwords in logs or client-side code

## Development

### Available Scripts
- `npm run dev` - Run both frontend and functions in development mode
- `npm run dev:frontend` - Run only the Vue.js frontend
- `npm run dev:functions` - Run only the Firebase Functions
- `npm run build` - Build both frontend and functions for production
- `npm run deploy` - Deploy to Firebase
- `npm run emulators` - Start Firebase emulators
- `npm run install:all` - Install dependencies for all modules

## Production Deployment

```bash
npm run build
npm run deploy
```

## Firebase Configuration

### Firestore Security Rules
The project includes security rules that deny direct client access to user data. All operations must go through the Cloud Functions API.

### Environment Variables
- Frontend: Configure Firebase project settings in `frontend/.env`
- Functions: Firebase Admin SDK is automatically configured in the Cloud Functions environment

## Features

### Admin Interface
- **User Management**: Create, edit, delete, and search users
- **Department & Unit Management**: Dynamic dropdowns with cascading selection
- **Dashboard**: Overview statistics and quick actions
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Updates**: Changes reflect immediately across the interface

### API Features
- **RESTful Design**: Standard HTTP methods and status codes
- **Input Validation**: Comprehensive validation on both client and server
- **Error Handling**: Detailed error messages and proper HTTP status codes
- **Pagination**: Efficient handling of large user lists
- **Search & Filter**: Find users by department, unit, or other criteria

### Security Features
- **Password Hashing**: Secure bcrypt hashing for all passwords
- **Input Sanitization**: Protection against injection attacks
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Database Security**: Firestore rules prevent direct client access
- **Environment Variables**: Sensitive data stored securely

## Troubleshooting

### Common Issues

#### Frontend Not Loading
- Check if all dependencies are installed: `npm run install:all`
- Verify Firebase configuration in `frontend/.env`
- Check browser console for JavaScript errors

#### API Calls Failing
- Ensure Firebase Functions are running: `npm run dev:functions`
- Check the API base URL in environment variables
- Verify Firebase project permissions

#### Database Connection Issues
- Confirm Firestore is enabled in Firebase console
- Check Firestore security rules
- Verify project ID matches in all configuration files

#### Deployment Issues
- Ensure Firebase CLI is installed and logged in
- Check Firebase project permissions
- Verify all environment variables are set

### Getting Help
- Check the [Firebase Documentation](https://firebase.google.com/docs)
- Review [Vue.js Documentation](https://vuejs.org/guide/)
- See [PrimeVue Documentation](https://primevue.org/)

## External Integration

Other webapps can authenticate users by making POST requests to the API endpoints. See the detailed examples above for implementation in various programming languages.

## Testing

### Setup Verification
Run the setup verification script:
```bash
node test-setup.js
```

### API Testing
Test the Firebase Functions API (requires functions to be running):
```bash
# Start Firebase Functions
npm run dev:functions

# In another terminal, run API tests
node test-api.js
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly using the provided test scripts
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

---

For detailed project structure and component explanations, see [PROJECT_BREAKDOWN.md](PROJECT_BREAKDOWN.md).
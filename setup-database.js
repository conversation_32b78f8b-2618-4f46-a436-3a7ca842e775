const admin = require('firebase-admin');

// Initialize Firebase Admin
admin.initializeApp({
  projectId: 'tkoh-central-auth' // Your actual project ID
});

const db = admin.firestore();

async function setupDatabase() {
  try {
    console.log('Setting up database...');
    
    // Create departments collection with initial departments
    const departmentsData = {
      departments: [
        'IT',
        'HR', 
        'Finance',
        'Marketing',
        'Operations',
        'Sales',
        'Legal',
        'Administration',
        'Engineering',
        'Customer Service'
      ],
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    await db.collection('departments').doc('list').set(departmentsData);
    console.log('Departments collection created successfully');

    // Create units collection with sample units
    const unitsData = [
      // IT Department Units
      { name: 'Software Development', department: 'IT' },
      { name: 'Infrastructure', department: 'IT' },
      { name: 'Cybersecurity', department: 'IT' },
      { name: 'Data Analytics', department: 'IT' },
      { name: 'Technical Support', department: 'IT' },
      
      // HR Department Units
      { name: 'Recruitment', department: 'HR' },
      { name: 'Employee Relations', department: 'HR' },
      { name: 'Training & Development', department: 'HR' },
      { name: 'Compensation & Benefits', department: 'HR' },
      
      // Finance Department Units
      { name: 'Accounting', department: 'Finance' },
      { name: 'Financial Planning', department: 'Finance' },
      { name: 'Audit', department: 'Finance' },
      { name: 'Treasury', department: 'Finance' },
      
      // Marketing Department Units
      { name: 'Digital Marketing', department: 'Marketing' },
      { name: 'Brand Management', department: 'Marketing' },
      { name: 'Market Research', department: 'Marketing' },
      { name: 'Content Creation', department: 'Marketing' },
      
      // Operations Department Units
      { name: 'Supply Chain', department: 'Operations' },
      { name: 'Quality Assurance', department: 'Operations' },
      { name: 'Process Improvement', department: 'Operations' },
      { name: 'Facilities Management', department: 'Operations' },
      
      // Sales Department Units
      { name: 'Inside Sales', department: 'Sales' },
      { name: 'Field Sales', department: 'Sales' },
      { name: 'Sales Operations', department: 'Sales' },
      { name: 'Business Development', department: 'Sales' },
      
      // Legal Department Units
      { name: 'Corporate Law', department: 'Legal' },
      { name: 'Compliance', department: 'Legal' },
      { name: 'Contracts', department: 'Legal' },
      
      // Administration Department Units
      { name: 'Executive Support', department: 'Administration' },
      { name: 'Office Management', department: 'Administration' },
      { name: 'Records Management', department: 'Administration' },
      
      // Engineering Department Units
      { name: 'Product Engineering', department: 'Engineering' },
      { name: 'Research & Development', department: 'Engineering' },
      { name: 'Quality Engineering', department: 'Engineering' },
      
      // Customer Service Department Units
      { name: 'Customer Support', department: 'Customer Service' },
      { name: 'Technical Support', department: 'Customer Service' },
      { name: 'Customer Success', department: 'Customer Service' }
    ];

    // Add each unit as a separate document
    const batch = db.batch();
    unitsData.forEach((unit, index) => {
      const unitRef = db.collection('units').doc(`unit_${index + 1}`);
      batch.set(unitRef, {
        ...unit,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    });

    await batch.commit();
    console.log('Units collection created successfully');

    console.log('Database setup completed successfully!');
    console.log('You can now run: node setup-database.js');

  } catch (error) {
    console.error('Error setting up database:', error);
  }
}

setupDatabase();

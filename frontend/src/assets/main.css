@import './base.css';

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

#app {
  min-height: 100vh;
  width: 100%;
  font-weight: normal;
  display: flex;
  flex-direction: column;
}

/* Responsive container */
.container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
}

/* Mobile-first responsive design */
@media (max-width: 576px) {
  .container {
    padding: 0.5rem;
  }

  .grid {
    gap: 0.5rem;
  }

  .col-12 {
    padding: 0.25rem;
  }
}

/* Tablet */
@media (min-width: 577px) and (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .grid {
    gap: 1rem;
  }
}

/* Desktop */
@media (min-width: 769px) {
  .container {
    padding: 1.5rem;
  }

  .grid {
    gap: 1.5rem;
  }
}

/* Large desktop */
@media (min-width: 1200px) {
  .container {
    padding: 2rem;
  }
}

/* Utility classes for responsive design */
.w-full {
  width: 100% !important;
}

.h-full {
  height: 100% !important;
}

.min-h-screen {
  min-height: 100vh !important;
}

/* PrimeVue responsive overrides */
.p-card {
  width: 100%;
  margin: 0;
}

.p-datatable {
  width: 100%;
}

.p-datatable .p-datatable-wrapper {
  overflow-x: auto;
}

/* Form responsiveness */
.user-form {
  width: 100%;
  padding: 1rem;
}

.user-form .grid {
  margin: 0;
}

.user-form .field {
  margin-bottom: 1rem;
}

/* Button responsiveness */
.p-button {
  width: auto;
  min-width: fit-content;
}

@media (max-width: 576px) {
  .p-button {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .p-toolbar .p-toolbar-group-end .p-button {
    width: auto;
    margin-bottom: 0;
    margin-left: 0.5rem;
  }
}

/* Navigation and header responsiveness */
.p-toolbar {
  flex-wrap: wrap;
  gap: 0.5rem;
}

.p-toolbar .p-toolbar-group-start,
.p-toolbar .p-toolbar-group-end {
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* Dashboard responsiveness */
.dashboard-content .grid {
  margin: 0;
}

.stats-card {
  margin-bottom: 1rem;
}

/* Table responsiveness */
@media (max-width: 768px) {
  .p-datatable .p-datatable-thead > tr > th,
  .p-datatable .p-datatable-tbody > tr > td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  .p-datatable .p-column-title {
    font-size: 0.875rem;
  }
}

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5001/central-auth-project/us-central1/api'

export const useDepartmentsStore = defineStore('departments', () => {
  // State
  const departments = ref([])
  const units = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const departmentsList = computed(() => departments.value)
  const unitsList = computed(() => units.value)
  
  // Get units filtered by department
  const getUnitsByDepartment = computed(() => {
    return (departmentName) => {
      return units.value.filter(unit => unit.department === departmentName)
    }
  })

  // Actions
  const fetchDepartments = async () => {
    try {
      loading.value = true
      error.value = null

      const response = await axios.get(`${API_BASE_URL}/departments`)

      if (response.data.success) {
        departments.value = response.data.data.departments
        return { success: true, data: response.data.data.departments }
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      console.error('Fetch departments error:', err)
      error.value = err.response?.data?.message || err.message || 'Failed to fetch departments'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const fetchUnits = async (department = null) => {
    try {
      loading.value = true
      error.value = null

      const params = new URLSearchParams()
      if (department) params.append('department', department)

      const response = await axios.get(`${API_BASE_URL}/units?${params}`)

      if (response.data.success) {
        units.value = response.data.data.units
        return { success: true, data: response.data.data.units }
      } else {
        throw new Error(response.data.message)
      }
    } catch (err) {
      console.error('Fetch units error:', err)
      error.value = err.response?.data?.message || err.message || 'Failed to fetch units'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const fetchAllUnits = async () => {
    return await fetchUnits()
  }

  const fetchUnitsByDepartment = async (department) => {
    return await fetchUnits(department)
  }

  // Initialize data
  const initializeData = async () => {
    await Promise.all([
      fetchDepartments(),
      fetchAllUnits()
    ])
  }

  return {
    // State
    departments,
    units,
    loading,
    error,
    
    // Getters
    departmentsList,
    unitsList,
    getUnitsByDepartment,
    
    // Actions
    fetchDepartments,
    fetchUnits,
    fetchAllUnits,
    fetchUnitsByDepartment,
    initializeData
  }
})

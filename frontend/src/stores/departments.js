import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5001/central-auth-project/us-central1/api'

// Temporary mock data for development
const MOCK_DEPARTMENTS = [
  'IT', 'HR', 'Finance', 'Marketing', 'Operations',
  'Sales', 'Legal', 'Administration', 'Engineering', 'Customer Service'
]

const MOCK_UNITS = [
  // IT Department Units
  { id: 'unit_1', name: 'Software Development', department: 'IT' },
  { id: 'unit_2', name: 'Infrastructure', department: 'IT' },
  { id: 'unit_3', name: 'Cybersecurity', department: 'IT' },
  { id: 'unit_4', name: 'Data Analytics', department: 'IT' },
  { id: 'unit_5', name: 'Technical Support', department: 'IT' },

  // HR Department Units
  { id: 'unit_6', name: 'Recruitment', department: 'HR' },
  { id: 'unit_7', name: 'Employee Relations', department: 'HR' },
  { id: 'unit_8', name: 'Training & Development', department: 'HR' },
  { id: 'unit_9', name: 'Compensation & Benefits', department: 'HR' },

  // Finance Department Units
  { id: 'unit_10', name: 'Accounting', department: 'Finance' },
  { id: 'unit_11', name: 'Financial Planning', department: 'Finance' },
  { id: 'unit_12', name: 'Audit', department: 'Finance' },
  { id: 'unit_13', name: 'Treasury', department: 'Finance' },

  // Marketing Department Units
  { id: 'unit_14', name: 'Digital Marketing', department: 'Marketing' },
  { id: 'unit_15', name: 'Brand Management', department: 'Marketing' },
  { id: 'unit_16', name: 'Market Research', department: 'Marketing' },
  { id: 'unit_17', name: 'Content Creation', department: 'Marketing' },

  // Operations Department Units
  { id: 'unit_18', name: 'Supply Chain', department: 'Operations' },
  { id: 'unit_19', name: 'Quality Assurance', department: 'Operations' },
  { id: 'unit_20', name: 'Process Improvement', department: 'Operations' },
  { id: 'unit_21', name: 'Facilities Management', department: 'Operations' },

  // Sales Department Units
  { id: 'unit_22', name: 'Inside Sales', department: 'Sales' },
  { id: 'unit_23', name: 'Field Sales', department: 'Sales' },
  { id: 'unit_24', name: 'Sales Operations', department: 'Sales' },
  { id: 'unit_25', name: 'Business Development', department: 'Sales' },

  // Legal Department Units
  { id: 'unit_26', name: 'Corporate Law', department: 'Legal' },
  { id: 'unit_27', name: 'Compliance', department: 'Legal' },
  { id: 'unit_28', name: 'Contracts', department: 'Legal' },

  // Administration Department Units
  { id: 'unit_29', name: 'Executive Support', department: 'Administration' },
  { id: 'unit_30', name: 'Office Management', department: 'Administration' },
  { id: 'unit_31', name: 'Records Management', department: 'Administration' },

  // Engineering Department Units
  { id: 'unit_32', name: 'Product Engineering', department: 'Engineering' },
  { id: 'unit_33', name: 'Research & Development', department: 'Engineering' },
  { id: 'unit_34', name: 'Quality Engineering', department: 'Engineering' },

  // Customer Service Department Units
  { id: 'unit_35', name: 'Customer Support', department: 'Customer Service' },
  { id: 'unit_36', name: 'Technical Support', department: 'Customer Service' },
  { id: 'unit_37', name: 'Customer Success', department: 'Customer Service' }
]

// Flag to use mock data when API is not available
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true' || false

export const useDepartmentsStore = defineStore('departments', () => {
  // State
  const departments = ref([])
  const units = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const departmentsList = computed(() => departments.value)
  const unitsList = computed(() => units.value)

  // Get units filtered by department
  const getUnitsByDepartment = computed(() => {
    return (departmentName) => {
      return units.value.filter(unit => unit.department === departmentName)
    }
  })

  // Actions
  const fetchDepartments = async () => {
    try {
      loading.value = true
      error.value = null

      // Try API first, fallback to mock data
      try {
        const response = await axios.get(`${API_BASE_URL}/departments`)
        if (response.data.success) {
          departments.value = response.data.data.departments
          return { success: true, data: response.data.data.departments }
        } else {
          throw new Error(response.data.message)
        }
      } catch (apiError) {
        console.warn('API not available, using mock data:', apiError.message)
        // Use mock data as fallback
        departments.value = MOCK_DEPARTMENTS
        return { success: true, data: MOCK_DEPARTMENTS }
      }
    } catch (err) {
      console.error('Fetch departments error:', err)
      error.value = err.response?.data?.message || err.message || 'Failed to fetch departments'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const fetchUnits = async (department = null) => {
    try {
      loading.value = true
      error.value = null

      // Try API first, fallback to mock data
      try {
        const params = new URLSearchParams()
        if (department) params.append('department', department)

        const response = await axios.get(`${API_BASE_URL}/units?${params}`)

        if (response.data.success) {
          units.value = response.data.data.units
          return { success: true, data: response.data.data.units }
        } else {
          throw new Error(response.data.message)
        }
      } catch (apiError) {
        console.warn('API not available, using mock data:', apiError.message)
        // Use mock data as fallback
        let filteredUnits = MOCK_UNITS
        if (department) {
          filteredUnits = MOCK_UNITS.filter(unit => unit.department === department)
        }
        units.value = filteredUnits
        return { success: true, data: filteredUnits }
      }
    } catch (err) {
      console.error('Fetch units error:', err)
      error.value = err.response?.data?.message || err.message || 'Failed to fetch units'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const fetchAllUnits = async () => {
    return await fetchUnits()
  }

  const fetchUnitsByDepartment = async (department) => {
    return await fetchUnits(department)
  }

  // Initialize data
  const initializeData = async () => {
    await Promise.all([
      fetchDepartments(),
      fetchAllUnits()
    ])
  }

  return {
    // State
    departments,
    units,
    loading,
    error,

    // Getters
    departmentsList,
    unitsList,
    getUnitsByDepartment,

    // Actions
    fetchDepartments,
    fetchUnits,
    fetchAllUnits,
    fetchUnitsByDepartment,
    initializeData
  }
})

<template>
  <div class="create-user-view">
    <!-- Header -->
    <div class="create-user-header">
      <Toolbar class="mb-4">
        <template #start>
          <div class="flex align-items-center">
            <Button
              icon="pi pi-arrow-left"
              severity="secondary"
              @click="goBack"
              class="mr-3"
            />
            <h1 class="text-xl font-bold">Create New User</h1>
          </div>
        </template>

        <template #end>
          <div class="flex gap-2">
            <Button
              label="Cancel"
              severity="secondary"
              @click="goBack"
            />
            <Button
              label="Create User"
              @click="handleSubmit"
              :loading="usersStore.loading"
              :disabled="!isFormValid"
            />
          </div>
        </template>
      </Toolbar>
    </div>

    <!-- Form -->
    <div class="create-user-content">
      <Card>
        <template #header>
          <div class="card-header">
            <h2 class="text-lg font-semibold">User Information</h2>
            <p class="text-sm text-gray-600">Fill in the details for the new user account</p>
          </div>
        </template>

        <template #content>
          <form @submit.prevent="handleSubmit" class="user-form">
            <div class="grid">
              <!-- Employee Number -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="employeeNumber" class="block text-sm font-medium mb-2">
                    Employee Number <span class="text-red-500">*</span>
                  </label>
                  <InputText
                    id="employeeNumber"
                    v-model="formData.employeeNumber"
                    placeholder="Enter employee number"
                    class="w-full"
                    :class="{ 'p-invalid': errors.employeeNumber }"
                    @blur="validateField('employeeNumber')"
                  />
                  <small v-if="errors.employeeNumber" class="p-error">{{ errors.employeeNumber }}</small>
                </div>
              </div>

              <!-- Corporate ID -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="corpId" class="block text-sm font-medium mb-2">
                    Corporate ID <span class="text-red-500">*</span>
                  </label>
                  <InputText
                    id="corpId"
                    v-model="formData.corpId"
                    placeholder="Enter corporate ID"
                    class="w-full"
                    :class="{ 'p-invalid': errors.corpId }"
                    @blur="validateField('corpId')"
                  />
                  <small v-if="errors.corpId" class="p-error">{{ errors.corpId }}</small>
                </div>
              </div>

              <!-- Email -->
              <div class="col-12">
                <div class="field">
                  <label for="email" class="block text-sm font-medium mb-2">
                    Email Address <span class="text-red-500">*</span>
                  </label>
                  <InputText
                    id="email"
                    v-model="formData.email"
                    type="email"
                    placeholder="Enter email address"
                    class="w-full"
                    :class="{ 'p-invalid': errors.email }"
                    @blur="validateField('email')"
                  />
                  <small v-if="errors.email" class="p-error">{{ errors.email }}</small>
                </div>
              </div>

              <!-- Password -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="password" class="block text-sm font-medium mb-2">
                    Password <span class="text-red-500">*</span>
                  </label>
                  <Password
                    id="password"
                    v-model="formData.password"
                    placeholder="Enter password"
                    class="w-full"
                    :class="{ 'p-invalid': errors.password }"
                    toggleMask
                    :feedback="true"
                    @blur="validateField('password')"
                  />
                  <small v-if="errors.password" class="p-error">{{ errors.password }}</small>
                </div>
              </div>

              <!-- Confirm Password -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="confirmPassword" class="block text-sm font-medium mb-2">
                    Confirm Password <span class="text-red-500">*</span>
                  </label>
                  <Password
                    id="confirmPassword"
                    v-model="formData.confirmPassword"
                    placeholder="Confirm password"
                    class="w-full"
                    :class="{ 'p-invalid': errors.confirmPassword }"
                    toggleMask
                    :feedback="false"
                    @blur="validateField('confirmPassword')"
                  />
                  <small v-if="errors.confirmPassword" class="p-error">{{ errors.confirmPassword }}</small>
                </div>
              </div>

              <!-- Department -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="department" class="block text-sm font-medium mb-2">
                    Department <span class="text-red-500">*</span>
                  </label>
                  <Select
                    id="department"
                    v-model="formData.department"
                    :options="departments"
                    placeholder="Select department"
                    class="w-full"
                    :class="{ 'p-invalid': errors.department }"
                    @change="onDepartmentChange"
                  />
                  <small v-if="errors.department" class="p-error">{{ errors.department }}</small>
                </div>
              </div>

              <!-- Unit -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="unit" class="block text-sm font-medium mb-2">
                    Unit <span class="text-red-500">*</span>
                  </label>
                  <Select
                    id="unit"
                    v-model="formData.unit"
                    :options="availableUnits"
                    optionLabel="name"
                    optionValue="name"
                    placeholder="Select unit"
                    class="w-full"
                    :class="{ 'p-invalid': errors.unit }"
                    :disabled="!formData.department"
                    @change="validateField('unit')"
                    showClear
                  />
                  <small v-if="errors.unit" class="p-error">{{ errors.unit }}</small>
                </div>
              </div>

              <!-- Suspended Status -->
              <div class="col-12">
                <div class="field">
                  <div class="flex align-items-center">
                    <Checkbox
                      id="suspended"
                      v-model="formData.suspended"
                      binary
                    />
                    <label for="suspended" class="ml-2">
                      Create account as suspended
                    </label>
                  </div>
                  <small class="text-gray-600">
                    If checked, the user account will be created in a suspended state
                  </small>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions mt-4 pt-4 border-t">
              <div class="flex justify-content-end gap-2">
                <Button
                  label="Cancel"
                  severity="secondary"
                  @click="goBack"
                  type="button"
                />
                <Button
                  label="Create User"
                  type="submit"
                  :loading="usersStore.loading"
                  :disabled="!isFormValid"
                />
              </div>
            </div>
          </form>
        </template>
      </Card>
    </div>

    <!-- Toast for notifications -->
    <Toast />
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUsersStore } from '@/stores/users'
import { useDepartmentsStore } from '@/stores/departments'
import { useToast } from 'primevue/usetoast'

const router = useRouter()
const usersStore = useUsersStore()
const departmentsStore = useDepartmentsStore()
const toast = useToast()

// Form data
const formData = reactive({
  employeeNumber: '',
  corpId: '',
  email: '',
  password: '',
  confirmPassword: '',
  department: '',
  unit: '',
  suspended: false
})

// Validation errors
const errors = reactive({
  employeeNumber: '',
  corpId: '',
  email: '',
  password: '',
  confirmPassword: '',
  department: '',
  unit: ''
})

// Dynamic data from stores
const departments = computed(() => departmentsStore.departmentsList)
const availableUnits = computed(() => {
  if (!formData.department) return []
  return departmentsStore.getUnitsByDepartment(formData.department)
})

// Computed
const isFormValid = computed(() => {
  return formData.employeeNumber &&
         formData.corpId &&
         formData.email &&
         formData.password &&
         formData.confirmPassword &&
         formData.department &&
         formData.unit &&
         !Object.values(errors).some(error => error)
})

// Validation methods
const validateField = (fieldName) => {
  switch (fieldName) {
    case 'employeeNumber':
      if (!formData.employeeNumber) {
        errors.employeeNumber = 'Employee number is required'
      } else if (formData.employeeNumber.length < 3) {
        errors.employeeNumber = 'Employee number must be at least 3 characters'
      } else {
        errors.employeeNumber = ''
      }
      break

    case 'corpId':
      if (!formData.corpId) {
        errors.corpId = 'Corporate ID is required'
      } else if (formData.corpId.length < 3) {
        errors.corpId = 'Corporate ID must be at least 3 characters'
      } else {
        errors.corpId = ''
      }
      break

    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!formData.email) {
        errors.email = 'Email is required'
      } else if (!emailRegex.test(formData.email)) {
        errors.email = 'Please enter a valid email address'
      } else {
        errors.email = ''
      }
      break

    case 'password':
      if (!formData.password) {
        errors.password = 'Password is required'
      } else if (formData.password.length < 6) {
        errors.password = 'Password must be at least 6 characters'
      } else {
        errors.password = ''
        // Re-validate confirm password if it exists
        if (formData.confirmPassword) {
          validateField('confirmPassword')
        }
      }
      break

    case 'confirmPassword':
      if (!formData.confirmPassword) {
        errors.confirmPassword = 'Please confirm your password'
      } else if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match'
      } else {
        errors.confirmPassword = ''
      }
      break

    case 'department':
      if (!formData.department) {
        errors.department = 'Department is required'
      } else {
        errors.department = ''
      }
      break

    case 'unit':
      if (!formData.unit) {
        errors.unit = 'Unit is required'
      } else if (formData.unit.length < 2) {
        errors.unit = 'Unit must be at least 2 characters'
      } else {
        errors.unit = ''
      }
      break
  }
}

const validateForm = () => {
  Object.keys(formData).forEach(field => {
    if (field !== 'confirmPassword' && field !== 'suspended') {
      validateField(field)
    }
  })
  validateField('confirmPassword')
}

// Methods
const onDepartmentChange = () => {
  // Clear unit when department changes
  formData.unit = ''
  validateField('department')
}

const handleSubmit = async () => {
  validateForm()

  if (!isFormValid.value) {
    toast.add({
      severity: 'warn',
      summary: 'Validation Error',
      detail: 'Please fix the errors in the form',
      life: 5000
    })
    return
  }

  // Prepare user data (exclude confirmPassword)
  const userData = {
    employeeNumber: formData.employeeNumber,
    corpId: formData.corpId,
    email: formData.email,
    password: formData.password,
    department: formData.department,
    unit: formData.unit,
    suspended: formData.suspended
  }

  const result = await usersStore.createUser(userData)

  if (result.success) {
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'User created successfully',
      life: 3000
    })

    // Navigate back to users list
    router.push('/users')
  } else {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: result.error || 'Failed to create user',
      life: 5000
    })
  }
}

const goBack = () => {
  router.push('/users')
}

// Initialize data on component mount
onMounted(async () => {
  await departmentsStore.initializeData()
})
</script>

<style scoped>
.create-user-view {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 1rem;
}

.create-user-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin: -1rem -1rem 1rem -1rem;
}

.create-user-content {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.user-form {
  padding: 1.5rem;
}

.field {
  margin-bottom: 1.5rem;
}

.field:last-child {
  margin-bottom: 0;
}

.form-actions {
  background-color: #f8f9fa;
  margin: 1.5rem -1.5rem -1.5rem -1.5rem;
  padding: 1rem 1.5rem;
}

@media (max-width: 768px) {
  .create-user-view {
    padding: 0.5rem;
  }

  .create-user-header {
    margin: -0.5rem -0.5rem 1rem -0.5rem;
  }

  .card-header {
    padding: 1rem;
  }

  .user-form {
    padding: 1rem;
  }

  .form-actions {
    margin: 1rem -1rem -1rem -1rem;
    padding: 1rem;
  }
}
</style>

<template>
  <div class="users-view">
    <!-- Header -->
    <div class="users-header">
      <Toolbar class="mb-4">
        <template #start>
          <div class="flex align-items-center">
            <Button
              icon="pi pi-arrow-left"
              severity="secondary"
              @click="$router.push('/dashboard')"
              class="mr-3"
            />
            <h1 class="text-xl font-bold">User Management</h1>
          </div>
        </template>

        <template #end>
          <div class="flex gap-2">
            <Button
              icon="pi pi-plus"
              label="Add User"
              @click="$router.push('/users/create')"
            />
            <Button
              icon="pi pi-refresh"
              severity="secondary"
              @click="refreshUsers"
              :loading="usersStore.loading"
            />
          </div>
        </template>
      </Toolbar>
    </div>

    <!-- Search and Filters -->
    <Card class="mb-4">
      <template #content>
        <div class="grid">
          <div class="col-12 md:col-4">
            <div class="field">
              <label for="search" class="block text-sm font-medium mb-2">Search Users</label>
              <InputText
                id="search"
                v-model="searchTerm"
                placeholder="Search by employee number, name, email..."
                class="w-full"
                @input="onSearchInput"
              />
            </div>
          </div>

          <div class="col-12 md:col-3">
            <div class="field">
              <label for="department" class="block text-sm font-medium mb-2">Department</label>
              <Select
                id="department"
                v-model="selectedDepartment"
                :options="departments"
                placeholder="All Departments"
                class="w-full"
                @change="onFilterChange"
              />
            </div>
          </div>

          <div class="col-12 md:col-3">
            <div class="field">
              <label for="status" class="block text-sm font-medium mb-2">Status</label>
              <Select
                id="status"
                v-model="selectedStatus"
                :options="statusOptions"
                placeholder="All Status"
                class="w-full"
                @change="onFilterChange"
              />
            </div>
          </div>

          <div class="col-12 md:col-2">
            <div class="field">
              <label class="block text-sm font-medium mb-2">&nbsp;</label>
              <Button
                label="Clear Filters"
                severity="secondary"
                @click="clearFilters"
                class="w-full"
              />
            </div>
          </div>
        </div>
      </template>
    </Card>

    <!-- Users DataTable -->
    <Card>
      <template #content>
        <DataTable
          :value="filteredUsers"
          :loading="usersStore.loading"
          paginator
          :rows="20"
          :rowsPerPageOptions="[10, 20, 50]"
          sortMode="multiple"
          removableSort
          :globalFilterFields="['employeeNumber', 'corpId', 'email', 'department', 'unit']"
          responsiveLayout="scroll"
          class="users-table"
        >
          <template #empty>
            <div class="text-center py-4">
              <i class="pi pi-users text-4xl text-gray-400 mb-3"></i>
              <p class="text-gray-600">No users found</p>
              <Button
                label="Add First User"
                @click="$router.push('/users/create')"
                class="mt-2"
              />
            </div>
          </template>

          <Column field="employeeNumber" header="Employee #" sortable>
            <template #body="{ data }">
              <span class="font-medium">{{ data.employeeNumber }}</span>
            </template>
          </Column>

          <Column field="corpId" header="Corp ID" sortable />

          <Column field="email" header="Email" sortable>
            <template #body="{ data }">
              <a :href="`mailto:${data.email}`" class="text-primary hover:underline">
                {{ data.email }}
              </a>
            </template>
          </Column>

          <Column field="department" header="Department" sortable />

          <Column field="unit" header="Unit" sortable />

          <Column field="suspended" header="Status" sortable>
            <template #body="{ data }">
              <Tag
                :value="data.suspended ? 'Suspended' : 'Active'"
                :severity="data.suspended ? 'danger' : 'success'"
              />
            </template>
          </Column>

          <Column field="createdAt" header="Created" sortable>
            <template #body="{ data }">
              <span v-if="data.createdAt">
                {{ formatDate(data.createdAt) }}
              </span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </Column>

          <Column header="Actions" :exportable="false">
            <template #body="{ data }">
              <div class="flex gap-2">
                <Button
                  icon="pi pi-eye"
                  severity="info"
                  size="small"
                  @click="viewUser(data)"
                  v-tooltip.top="'View Details'"
                />
                <Button
                  icon="pi pi-pencil"
                  severity="secondary"
                  size="small"
                  @click="editUser(data)"
                  v-tooltip.top="'Edit User'"
                />
                <Button
                  :icon="data.suspended ? 'pi pi-check' : 'pi pi-ban'"
                  :severity="data.suspended ? 'success' : 'warning'"
                  size="small"
                  @click="toggleSuspension(data)"
                  v-tooltip.top="data.suspended ? 'Activate User' : 'Suspend User'"
                />
                <Button
                  icon="pi pi-trash"
                  severity="danger"
                  size="small"
                  @click="confirmDelete(data)"
                  v-tooltip.top="'Delete User'"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </template>
    </Card>

    <!-- User Details Dialog -->
    <Dialog
      v-model="showUserDialog"
      :header="`User Details - ${selectedUser?.employeeNumber}`"
      :style="{ width: '600px' }"
      modal
    >
      <div v-if="selectedUser" class="user-details">
        <div class="grid">
          <div class="col-6">
            <div class="field">
              <label class="font-medium">Employee Number:</label>
              <p>{{ selectedUser.employeeNumber }}</p>
            </div>
          </div>
          <div class="col-6">
            <div class="field">
              <label class="font-medium">Corporate ID:</label>
              <p>{{ selectedUser.corpId }}</p>
            </div>
          </div>
          <div class="col-12">
            <div class="field">
              <label class="font-medium">Email:</label>
              <p>{{ selectedUser.email }}</p>
            </div>
          </div>
          <div class="col-6">
            <div class="field">
              <label class="font-medium">Department:</label>
              <p>{{ selectedUser.department }}</p>
            </div>
          </div>
          <div class="col-6">
            <div class="field">
              <label class="font-medium">Unit:</label>
              <p>{{ selectedUser.unit }}</p>
            </div>
          </div>
          <div class="col-6">
            <div class="field">
              <label class="font-medium">Status:</label>
              <Tag
                :value="selectedUser.suspended ? 'Suspended' : 'Active'"
                :severity="selectedUser.suspended ? 'danger' : 'success'"
              />
            </div>
          </div>
          <div class="col-6">
            <div class="field">
              <label class="font-medium">Created:</label>
              <p>{{ formatDate(selectedUser.createdAt) }}</p>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <Button
          label="Edit"
          icon="pi pi-pencil"
          @click="editUser(selectedUser)"
        />
        <Button
          label="Close"
          severity="secondary"
          @click="showUserDialog = false"
        />
      </template>
    </Dialog>

    <!-- Delete Confirmation Dialog -->
    <ConfirmDialog />

    <!-- Toast for notifications -->
    <Toast />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUsersStore } from '@/stores/users'
import { useToast } from 'primevue/usetoast'
import { useConfirm } from 'primevue/useconfirm'

const router = useRouter()
const usersStore = useUsersStore()
const toast = useToast()
const confirm = useConfirm()

// Reactive data
const searchTerm = ref('')
const selectedDepartment = ref(null)
const selectedStatus = ref(null)
const showUserDialog = ref(false)
const selectedUser = ref(null)

// Filter options
const departments = ref([
  'IT', 'HR', 'Finance', 'Marketing', 'Operations', 'Sales'
])

const statusOptions = ref([
  { label: 'Active', value: false },
  { label: 'Suspended', value: true }
])

// Computed
const filteredUsers = computed(() => {
  let users = [...usersStore.users]

  // Search filter
  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    users = users.filter(user =>
      user.employeeNumber?.toLowerCase().includes(search) ||
      user.corpId?.toLowerCase().includes(search) ||
      user.email?.toLowerCase().includes(search) ||
      user.department?.toLowerCase().includes(search) ||
      user.unit?.toLowerCase().includes(search)
    )
  }

  // Department filter
  if (selectedDepartment.value) {
    users = users.filter(user => user.department === selectedDepartment.value)
  }

  // Status filter
  if (selectedStatus.value !== null) {
    users = users.filter(user => user.suspended === selectedStatus.value)
  }

  return users
})

// Methods
const refreshUsers = async () => {
  const result = await usersStore.fetchUsers({ limit: 100 })
  if (result.success) {
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Users refreshed successfully',
      life: 3000
    })
  } else {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: result.error || 'Failed to refresh users',
      life: 5000
    })
  }
}

const onSearchInput = () => {
  // Debounce search if needed
}

const onFilterChange = () => {
  // Filter change handler
}

const clearFilters = () => {
  searchTerm.value = ''
  selectedDepartment.value = null
  selectedStatus.value = null
}

const viewUser = (user) => {
  selectedUser.value = user
  showUserDialog.value = true
}

const editUser = (user) => {
  router.push(`/users/${user.employeeNumber}/edit`)
}

const toggleSuspension = async (user) => {
  const action = user.suspended ? 'activate' : 'suspend'
  const newStatus = !user.suspended

  confirm.require({
    message: `Are you sure you want to ${action} this user?`,
    header: `${action.charAt(0).toUpperCase() + action.slice(1)} User`,
    icon: 'pi pi-exclamation-triangle',
    accept: async () => {
      const result = await usersStore.updateUser(user.employeeNumber, {
        suspended: newStatus
      })

      if (result.success) {
        toast.add({
          severity: 'success',
          summary: 'Success',
          detail: `User ${action}d successfully`,
          life: 3000
        })
      } else {
        toast.add({
          severity: 'error',
          summary: 'Error',
          detail: result.error || `Failed to ${action} user`,
          life: 5000
        })
      }
    }
  })
}

const confirmDelete = (user) => {
  confirm.require({
    message: `Are you sure you want to delete user ${user.employeeNumber}? This action cannot be undone.`,
    header: 'Delete User',
    icon: 'pi pi-exclamation-triangle',
    acceptClass: 'p-button-danger',
    accept: async () => {
      const result = await usersStore.deleteUser(user.employeeNumber)

      if (result.success) {
        toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'User deleted successfully',
          life: 3000
        })
      } else {
        toast.add({
          severity: 'error',
          summary: 'Error',
          detail: result.error || 'Failed to delete user',
          life: 5000
        })
      }
    }
  })
}

const formatDate = (date) => {
  if (!date) return '-'

  // Handle Firestore timestamp
  if (date.toDate) {
    return date.toDate().toLocaleDateString()
  }

  // Handle regular date
  if (date instanceof Date) {
    return date.toLocaleDateString()
  }

  // Handle string date
  return new Date(date).toLocaleDateString()
}

// Lifecycle
onMounted(async () => {
  if (usersStore.users.length === 0) {
    await refreshUsers()
  }
})
</script>

<style scoped>
.users-view {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 1rem;
}

.users-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin: -1rem -1rem 1rem -1rem;
}

.users-table {
  font-size: 0.9rem;
}

.users-table .p-datatable-tbody > tr > td {
  padding: 0.75rem;
}

.user-details .field {
  margin-bottom: 1rem;
}

.user-details .field label {
  display: block;
  margin-bottom: 0.25rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.user-details .field p {
  margin: 0;
  font-size: 0.95rem;
}

@media (max-width: 768px) {
  .users-view {
    padding: 0.5rem;
  }

  .users-header {
    margin: -0.5rem -0.5rem 1rem -0.5rem;
  }

  .users-table {
    font-size: 0.8rem;
  }

  .users-table .p-datatable-tbody > tr > td {
    padding: 0.5rem;
  }
}
</style>

<template>
  <div class="edit-user-view">
    <!-- Header -->
    <div class="edit-user-header">
      <Toolbar class="mb-4">
        <template #start>
          <div class="flex align-items-center">
            <Button
              icon="pi pi-arrow-left"
              severity="secondary"
              @click="goBack"
              class="mr-3"
            />
            <h1 class="text-xl font-bold">Edit User</h1>
            <span v-if="currentUser" class="ml-3 text-gray-600">
              {{ currentUser.employeeNumber }}
            </span>
          </div>
        </template>

        <template #end>
          <div class="flex gap-2">
            <Button
              label="Cancel"
              severity="secondary"
              @click="goBack"
            />
            <Button
              label="Save Changes"
              @click="handleSubmit"
              :loading="usersStore.loading"
              :disabled="!isFormValid || !hasChanges"
            />
          </div>
        </template>
      </Toolbar>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-content-center align-items-center" style="height: 400px;">
      <ProgressSpinner />
    </div>

    <!-- Form -->
    <div v-else-if="currentUser" class="edit-user-content">
      <Card>
        <template #header>
          <div class="card-header">
            <h2 class="text-lg font-semibold">User Information</h2>
            <p class="text-sm text-gray-600">Update the user account details</p>
          </div>
        </template>

        <template #content>
          <form @submit.prevent="handleSubmit" class="user-form">
            <div class="grid">
              <!-- Employee Number (Read-only) -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="employeeNumber" class="block text-sm font-medium mb-2">
                    Employee Number
                  </label>
                  <InputText
                    id="employeeNumber"
                    :value="currentUser.employeeNumber"
                    class="w-full"
                    readonly
                    disabled
                  />
                  <small class="text-gray-600">Employee number cannot be changed</small>
                </div>
              </div>

              <!-- Corporate ID -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="corpId" class="block text-sm font-medium mb-2">
                    Corporate ID <span class="text-red-500">*</span>
                  </label>
                  <InputText
                    id="corpId"
                    v-model="formData.corpId"
                    placeholder="Enter corporate ID"
                    class="w-full"
                    :class="{ 'p-invalid': errors.corpId }"
                    @blur="validateField('corpId')"
                  />
                  <small v-if="errors.corpId" class="p-error">{{ errors.corpId }}</small>
                </div>
              </div>

              <!-- Email -->
              <div class="col-12">
                <div class="field">
                  <label for="email" class="block text-sm font-medium mb-2">
                    Email Address <span class="text-red-500">*</span>
                  </label>
                  <InputText
                    id="email"
                    v-model="formData.email"
                    type="email"
                    placeholder="Enter email address"
                    class="w-full"
                    :class="{ 'p-invalid': errors.email }"
                    @blur="validateField('email')"
                  />
                  <small v-if="errors.email" class="p-error">{{ errors.email }}</small>
                </div>
              </div>

              <!-- Password (Optional for updates) -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="password" class="block text-sm font-medium mb-2">
                    New Password
                  </label>
                  <Password
                    id="password"
                    v-model="formData.password"
                    placeholder="Leave blank to keep current password"
                    class="w-full"
                    :class="{ 'p-invalid': errors.password }"
                    toggleMask
                    :feedback="true"
                    @blur="validateField('password')"
                  />
                  <small v-if="errors.password" class="p-error">{{ errors.password }}</small>
                  <small v-else class="text-gray-600">Leave blank to keep current password</small>
                </div>
              </div>

              <!-- Confirm Password -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="confirmPassword" class="block text-sm font-medium mb-2">
                    Confirm New Password
                  </label>
                  <Password
                    id="confirmPassword"
                    v-model="formData.confirmPassword"
                    placeholder="Confirm new password"
                    class="w-full"
                    :class="{ 'p-invalid': errors.confirmPassword }"
                    toggleMask
                    :feedback="false"
                    @blur="validateField('confirmPassword')"
                    :disabled="!formData.password"
                  />
                  <small v-if="errors.confirmPassword" class="p-error">{{ errors.confirmPassword }}</small>
                </div>
              </div>

              <!-- Department -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="department" class="block text-sm font-medium mb-2">
                    Department <span class="text-red-500">*</span>
                  </label>
                  <Select
                    id="department"
                    v-model="formData.department"
                    :options="departments"
                    placeholder="Select department"
                    class="w-full"
                    :class="{ 'p-invalid': errors.department }"
                    @change="onDepartmentChange"
                  />
                  <small v-if="errors.department" class="p-error">{{ errors.department }}</small>
                </div>
              </div>

              <!-- Unit -->
              <div class="col-12 md:col-6">
                <div class="field">
                  <label for="unit" class="block text-sm font-medium mb-2">
                    Unit <span class="text-red-500">*</span>
                  </label>
                  <Select
                    id="unit"
                    v-model="formData.unit"
                    :options="availableUnits"
                    optionLabel="name"
                    optionValue="name"
                    placeholder="Select unit"
                    class="w-full"
                    :class="{ 'p-invalid': errors.unit }"
                    :disabled="!formData.department"
                    @change="validateField('unit')"
                    showClear
                  />
                  <small v-if="errors.unit" class="p-error">{{ errors.unit }}</small>
                </div>
              </div>

              <!-- Suspended Status -->
              <div class="col-12">
                <div class="field">
                  <div class="flex align-items-center">
                    <Checkbox
                      id="suspended"
                      v-model="formData.suspended"
                      binary
                    />
                    <label for="suspended" class="ml-2">
                      Account is suspended
                    </label>
                  </div>
                  <small class="text-gray-600">
                    Suspended users cannot authenticate
                  </small>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions mt-4 pt-4 border-t">
              <div class="flex justify-content-end gap-2">
                <Button
                  label="Cancel"
                  severity="secondary"
                  @click="goBack"
                  type="button"
                />
                <Button
                  label="Save Changes"
                  type="submit"
                  :loading="usersStore.loading"
                  :disabled="!isFormValid || !hasChanges"
                />
              </div>
            </div>
          </form>
        </template>
      </Card>
    </div>

    <!-- User Not Found -->
    <div v-else class="flex justify-content-center align-items-center" style="height: 400px;">
      <Card>
        <template #content>
          <div class="text-center">
            <i class="pi pi-exclamation-triangle text-4xl text-orange-500 mb-3"></i>
            <h3 class="text-lg font-semibold mb-2">User Not Found</h3>
            <p class="text-gray-600 mb-4">The requested user could not be found.</p>
            <Button label="Back to Users" @click="goBack" />
          </div>
        </template>
      </Card>
    </div>

    <!-- Toast for notifications -->
    <Toast />
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUsersStore } from '@/stores/users'
import { useDepartmentsStore } from '@/stores/departments'
import { useToast } from 'primevue/usetoast'

const router = useRouter()
const route = useRoute()
const usersStore = useUsersStore()
const departmentsStore = useDepartmentsStore()
const toast = useToast()

// Reactive data
const loading = ref(true)
const currentUser = ref(null)
const originalData = ref(null)

// Form data
const formData = reactive({
  corpId: '',
  email: '',
  password: '',
  confirmPassword: '',
  department: '',
  unit: '',
  suspended: false
})

// Validation errors
const errors = reactive({
  corpId: '',
  email: '',
  password: '',
  confirmPassword: '',
  department: '',
  unit: ''
})

// Dynamic data from stores
const departments = computed(() => departmentsStore.departmentsList)
const availableUnits = computed(() => {
  if (!formData.department) return []
  return departmentsStore.getUnitsByDepartment(formData.department)
})

// Computed
const isFormValid = computed(() => {
  return formData.corpId &&
         formData.email &&
         formData.department &&
         formData.unit &&
         !Object.values(errors).some(error => error)
})

const hasChanges = computed(() => {
  if (!originalData.value) return false

  return formData.corpId !== originalData.value.corpId ||
         formData.email !== originalData.value.email ||
         formData.department !== originalData.value.department ||
         formData.unit !== originalData.value.unit ||
         formData.suspended !== originalData.value.suspended ||
         formData.password !== ''
})

// Methods
const onDepartmentChange = () => {
  // Clear unit when department changes
  formData.unit = ''
  validateField('department')
}

const loadUser = async () => {
  loading.value = true
  const employeeNumber = route.params.employeeNumber

  const result = await usersStore.fetchUser(employeeNumber)

  if (result.success) {
    currentUser.value = result.data
    originalData.value = { ...result.data }

    // Populate form
    formData.corpId = result.data.corpId || ''
    formData.email = result.data.email || ''
    formData.department = result.data.department || ''
    formData.unit = result.data.unit || ''
    formData.suspended = result.data.suspended || false
    formData.password = ''
    formData.confirmPassword = ''
  } else {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: result.error || 'Failed to load user',
      life: 5000
    })
  }

  loading.value = false
}

// Validation methods
const validateField = (fieldName) => {
  switch (fieldName) {
    case 'corpId':
      if (!formData.corpId) {
        errors.corpId = 'Corporate ID is required'
      } else if (formData.corpId.length < 3) {
        errors.corpId = 'Corporate ID must be at least 3 characters'
      } else {
        errors.corpId = ''
      }
      break

    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!formData.email) {
        errors.email = 'Email is required'
      } else if (!emailRegex.test(formData.email)) {
        errors.email = 'Please enter a valid email address'
      } else {
        errors.email = ''
      }
      break

    case 'password':
      if (formData.password && formData.password.length < 6) {
        errors.password = 'Password must be at least 6 characters'
      } else {
        errors.password = ''
        // Re-validate confirm password if it exists
        if (formData.confirmPassword) {
          validateField('confirmPassword')
        }
      }
      break

    case 'confirmPassword':
      if (formData.password && !formData.confirmPassword) {
        errors.confirmPassword = 'Please confirm your password'
      } else if (formData.password && formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match'
      } else {
        errors.confirmPassword = ''
      }
      break

    case 'department':
      if (!formData.department) {
        errors.department = 'Department is required'
      } else {
        errors.department = ''
      }
      break

    case 'unit':
      if (!formData.unit) {
        errors.unit = 'Unit is required'
      } else if (formData.unit.length < 2) {
        errors.unit = 'Unit must be at least 2 characters'
      } else {
        errors.unit = ''
      }
      break
  }
}

const validateForm = () => {
  Object.keys(formData).forEach(field => {
    if (field !== 'confirmPassword' && field !== 'suspended') {
      validateField(field)
    }
  })
  if (formData.password) {
    validateField('confirmPassword')
  }
}

const handleSubmit = async () => {
  validateForm()

  if (!isFormValid.value) {
    toast.add({
      severity: 'warn',
      summary: 'Validation Error',
      detail: 'Please fix the errors in the form',
      life: 5000
    })
    return
  }

  if (!hasChanges.value) {
    toast.add({
      severity: 'info',
      summary: 'No Changes',
      detail: 'No changes were made to save',
      life: 3000
    })
    return
  }

  // Prepare update data
  const updateData = {
    corpId: formData.corpId,
    email: formData.email,
    department: formData.department,
    unit: formData.unit,
    suspended: formData.suspended
  }

  // Only include password if it was changed
  if (formData.password) {
    updateData.password = formData.password
  }

  const result = await usersStore.updateUser(currentUser.value.employeeNumber, updateData)

  if (result.success) {
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'User updated successfully',
      life: 3000
    })

    // Navigate back to users list
    router.push('/users')
  } else {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: result.error || 'Failed to update user',
      life: 5000
    })
  }
}

const goBack = () => {
  router.push('/users')
}

// Watch for password changes to clear confirm password
watch(() => formData.password, (newPassword) => {
  if (!newPassword) {
    formData.confirmPassword = ''
    errors.confirmPassword = ''
  }
})

// Lifecycle
onMounted(async () => {
  await departmentsStore.initializeData()
  loadUser()
})
</script>

<style scoped>
.edit-user-view {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 1rem;
}

.edit-user-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin: -1rem -1rem 1rem -1rem;
}

.edit-user-content {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.user-form {
  padding: 1.5rem;
}

.field {
  margin-bottom: 1.5rem;
}

.field:last-child {
  margin-bottom: 0;
}

.form-actions {
  background-color: #f8f9fa;
  margin: 1.5rem -1.5rem -1.5rem -1.5rem;
  padding: 1rem 1.5rem;
}

@media (max-width: 768px) {
  .edit-user-view {
    padding: 0.5rem;
  }

  .edit-user-header {
    margin: -0.5rem -0.5rem 1rem -0.5rem;
  }

  .card-header {
    padding: 1rem;
  }

  .user-form {
    padding: 1rem;
  }

  .form-actions {
    margin: 1rem -1rem -1rem -1rem;
    padding: 1rem;
  }
}
</style>

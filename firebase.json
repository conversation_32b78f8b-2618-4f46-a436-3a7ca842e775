{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}], "hosting": {"public": "frontend/dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/api/**", "function": "api"}, {"source": "**", "destination": "/index.html"}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "emulators": {"functions": {"port": 5001, "host": "localhost"}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}